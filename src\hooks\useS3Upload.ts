import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import axios from 'axios';
import { toast } from 'sonner';
import { API_ROUTES } from '@/constants/api.routes';
import axiosInstance from '@/lib/axios';
import {
  IS3UploadVariables,
  IPresignedUrlResponse,
  IS3UploadResponse
} from '@/types/mutation.types';
import { ApiError } from '@/types/common.types';

const uploadS3Image = async ({
  file,
  fileName,
  path
}: IS3UploadVariables): Promise<IS3UploadResponse> => {
  try {
    // Step 1: Get presigned URL
    const presigned = await axiosInstance.get<IPresignedUrlResponse>(
      `${API_ROUTES.UPLOAD.PRESIGNED_URL}?fileName=${fileName}&fileSize=${file.size}&mimeType=${file.type}&path=${path}`
    );

    const { key, presignedUrl } = presigned.data.data;

    // Step 2: Upload file to S3
    const uploadRes = await axios.put(presignedUrl, file, {
      headers: { 'Content-Type': file.type }
    });

    if (uploadRes.status === 200) {
      return { url: `${process.env.NEXT_PUBLIC_AWS_BUCKET_URL}/${key}` };
    }

    throw new Error('Upload to S3 failed');
  } catch (error) {
    throw error;
  }
};

export const useS3ImageUpload = <TError = ApiError, TContext = unknown>(
  options?: UseMutationOptions<
    IS3UploadResponse,
    TError,
    IS3UploadVariables,
    TContext
  >
) => {
  const { mutateAsync, isPending, ...props } = useMutation<
    IS3UploadResponse,
    TError,
    IS3UploadVariables,
    TContext
  >({
    mutationKey: ['s3-image-upload'],
    mutationFn: uploadS3Image,
    onSuccess: (data) => {
      toast.success('Image uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to upload image');
    },
    ...options
  });

  return { upload: mutateAsync, isUploading: isPending, ...props };
};
